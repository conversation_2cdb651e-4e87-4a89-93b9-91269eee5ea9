/**
 * Price estimation utilities for custom orders
 */

export interface CustomOrderSpecs {
  size: 'small' | 'medium' | 'large' | 'extra-large';
  bedazzlingLevel: 'light' | 'medium' | 'heavy' | 'premium';
  frameOption: 'basic' | 'premium' | 'luxury';
  complexity: 'simple' | 'detailed' | 'intricate';
  quantity: number;
  rushOrder: boolean;
  additionalFeatures: string[];
}

// Base prices in KES
const BASE_PRICES = {
  size: {
    small: 8000,
    medium: 12000,
    large: 18000,
    'extra-large': 25000,
  },
  bedazzlingLevel: {
    light: 0,
    medium: 3000,
    heavy: 6000,
    premium: 12000,
  },
  frameOption: {
    basic: 0,
    premium: 2500,
    luxury: 6000,
  },
  complexity: {
    simple: 0,
    detailed: 4000,
    intricate: 8000,
  },
};

// Additional features pricing
const FEATURE_PRICES = {
  'custom-background': 3000,
  'multiple-subjects': 2000,
  'special-effects': 4000,
  'premium-packaging': 1500,
  'certificate-authenticity': 1000,
  'digital-copy': 2000,
};

// Quantity discounts
const QUANTITY_DISCOUNTS = [
  { min: 1, max: 1, discount: 0 },
  { min: 2, max: 3, discount: 0.05 }, // 5% off
  { min: 4, max: 5, discount: 0.10 }, // 10% off
  { min: 6, max: 10, discount: 0.15 }, // 15% off
  { min: 11, max: Infinity, discount: 0.20 }, // 20% off
];

export function calculateEstimatedPrice(specs: CustomOrderSpecs): {
  basePrice: number;
  sizePrice: number;
  bedazzlingPrice: number;
  framePrice: number;
  complexityPrice: number;
  featuresPrice: number;
  subtotal: number;
  quantityDiscount: number;
  rushOrderFee: number;
  total: number;
  breakdown: Array<{ label: string; amount: number; }>;
} {
  // Base price calculation
  const sizePrice = BASE_PRICES.size[specs.size];
  const bedazzlingPrice = BASE_PRICES.bedazzlingLevel[specs.bedazzlingLevel];
  const framePrice = BASE_PRICES.frameOption[specs.frameOption];
  const complexityPrice = BASE_PRICES.complexity[specs.complexity];
  
  // Additional features
  const featuresPrice = specs.additionalFeatures.reduce((total, feature) => {
    return total + (FEATURE_PRICES[feature as keyof typeof FEATURE_PRICES] || 0);
  }, 0);
  
  // Subtotal per item
  const basePrice = sizePrice;
  const perItemPrice = basePrice + bedazzlingPrice + framePrice + complexityPrice + featuresPrice;
  const subtotal = perItemPrice * specs.quantity;
  
  // Quantity discount
  const discountRate = QUANTITY_DISCOUNTS.find(
    d => specs.quantity >= d.min && specs.quantity <= d.max
  )?.discount || 0;
  const quantityDiscount = subtotal * discountRate;
  
  // Rush order fee (50% surcharge)
  const rushOrderFee = specs.rushOrder ? (subtotal - quantityDiscount) * 0.5 : 0;
  
  // Final total
  const total = subtotal - quantityDiscount + rushOrderFee;
  
  // Breakdown for display
  const breakdown = [
    { label: `Base Price (${specs.size})`, amount: sizePrice },
    { label: `Bedazzling (${specs.bedazzlingLevel})`, amount: bedazzlingPrice },
    { label: `Frame (${specs.frameOption})`, amount: framePrice },
    { label: `Complexity (${specs.complexity})`, amount: complexityPrice },
    ...(featuresPrice > 0 ? [{ label: 'Additional Features', amount: featuresPrice }] : []),
    { label: `Quantity (${specs.quantity} items)`, amount: perItemPrice * specs.quantity },
    ...(quantityDiscount > 0 ? [{ label: `Quantity Discount (${Math.round(discountRate * 100)}%)`, amount: -quantityDiscount }] : []),
    ...(rushOrderFee > 0 ? [{ label: 'Rush Order Fee (50%)', amount: rushOrderFee }] : []),
  ];
  
  return {
    basePrice,
    sizePrice,
    bedazzlingPrice,
    framePrice,
    complexityPrice,
    featuresPrice,
    subtotal,
    quantityDiscount,
    rushOrderFee,
    total,
    breakdown,
  };
}

export function getEstimatedDeliveryTime(specs: CustomOrderSpecs): {
  minDays: number;
  maxDays: number;
  description: string;
} {
  let baseDays = 14; // 2 weeks base
  
  // Adjust based on complexity
  switch (specs.complexity) {
    case 'detailed':
      baseDays += 7;
      break;
    case 'intricate':
      baseDays += 14;
      break;
  }
  
  // Adjust based on bedazzling level
  switch (specs.bedazzlingLevel) {
    case 'heavy':
      baseDays += 3;
      break;
    case 'premium':
      baseDays += 7;
      break;
  }
  
  // Adjust based on quantity
  if (specs.quantity > 3) {
    baseDays += Math.ceil(specs.quantity / 2);
  }
  
  // Rush order
  if (specs.rushOrder) {
    baseDays = Math.ceil(baseDays * 0.6); // 40% faster
  }
  
  const minDays = Math.max(baseDays - 3, specs.rushOrder ? 7 : 10);
  const maxDays = baseDays + 5;
  
  let description = `${minDays}-${maxDays} business days`;
  if (specs.rushOrder) {
    description += ' (Rush Order)';
  }
  
  return { minDays, maxDays, description };
}

// Available additional features
export const ADDITIONAL_FEATURES = [
  { id: 'custom-background', name: 'Custom Background', price: 3000, description: 'Personalized background design' },
  { id: 'multiple-subjects', name: 'Multiple Subjects', price: 2000, description: 'Additional people or objects' },
  { id: 'special-effects', name: 'Special Effects', price: 4000, description: 'Lighting effects, shadows, etc.' },
  { id: 'premium-packaging', name: 'Premium Packaging', price: 1500, description: 'Luxury gift box with certificate' },
  { id: 'certificate-authenticity', name: 'Certificate of Authenticity', price: 1000, description: 'Official authenticity certificate' },
  { id: 'digital-copy', name: 'High-Resolution Digital Copy', price: 2000, description: 'Digital version for printing' },
];
