'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  PhotoIcon,
  SparklesIcon,
  ClockIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  CalculatorIcon,
  InformationCircleIcon,
  PlusIcon,
  MinusIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatPrice } from '@/lib/utils';
import {
  calculateEstimatedPrice,
  getEstimatedDeliveryTime,
  ADDITIONAL_FEATURES,
  type CustomOrderSpecs
} from '@/lib/price-estimation';
import { useAuth } from '@/contexts/auth-context';

export default function CustomOrdersPage() {
  const { isAuthenticated } = useAuth();

  const [formData, setFormData] = useState({
    customerName: '',
    email: '',
    phone: '',
    description: '',
    referenceImages: [] as File[]
  });

  const [specs, setSpecs] = useState<CustomOrderSpecs>({
    size: 'medium',
    bedazzlingLevel: 'medium',
    frameOption: 'premium',
    complexity: 'simple',
    quantity: 1,
    rushOrder: false,
    additionalFeatures: []
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [estimation, setEstimation] = useState(calculateEstimatedPrice(specs));
  const [deliveryTime, setDeliveryTime] = useState(getEstimatedDeliveryTime(specs));

  // Update estimation when specs change
  useEffect(() => {
    setEstimation(calculateEstimatedPrice(specs));
    setDeliveryTime(getEstimatedDeliveryTime(specs));
  }, [specs]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSpecChange = (field: keyof CustomOrderSpecs, value: any) => {
    setSpecs(prev => ({ ...prev, [field]: value }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFormData(prev => ({
        ...prev,
        referenceImages: [...prev.referenceImages, ...Array.from(e.target.files!)]
      }));
    }
  };

  const toggleFeature = (featureId: string) => {
    setSpecs(prev => ({
      ...prev,
      additionalFeatures: prev.additionalFeatures.includes(featureId)
        ? prev.additionalFeatures.filter(f => f !== featureId)
        : [...prev.additionalFeatures, featureId]
    }));
  };

  const adjustQuantity = (delta: number) => {
    setSpecs(prev => ({
      ...prev,
      quantity: Math.max(1, prev.quantity + delta)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isAuthenticated) {
      window.location.href = '/auth/login';
      return;
    }

    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      alert(`Custom order request submitted successfully! Estimated total: ${formatPrice(estimation.total)}`);
    }, 2000);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-12"
      >
        <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-6">
          Custom Bedazzled Portraits
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
          Transform your precious memories into stunning, one-of-a-kind rhinestone artwork.
          Each piece is meticulously handcrafted by our Kenyan artisans with premium materials.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Badge variant="secondary" className="text-sm px-4 py-2">
            ✨ Premium Rhinestones
          </Badge>
          <Badge variant="secondary" className="text-sm px-4 py-2">
            🎨 Custom Design
          </Badge>
          <Badge variant="secondary" className="text-sm px-4 py-2">
            🚚 Free Delivery in Nairobi
          </Badge>
          <Badge variant="secondary" className="text-sm px-4 py-2">
            💎 Lifetime Guarantee
          </Badge>
        </div>
      </motion.div>

      {/* Main Form Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Form */}
        <div className="lg:col-span-2">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SparklesIcon className="h-6 w-6 text-accent" />
                  Custom Order Details
                </CardTitle>
                <CardDescription>
                  Configure your custom bedazzled portrait and see real-time pricing
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Customer Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground">Customer Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Full Name *
                        </label>
                        <Input
                          type="text"
                          value={formData.customerName}
                          onChange={(e) => handleInputChange('customerName', e.target.value)}
                          placeholder="Enter your full name"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Email Address *
                        </label>
                        <Input
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          placeholder="<EMAIL>"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Phone Number *
                        </label>
                        <Input
                          type="tel"
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          placeholder="+254 700 000 000"
                          required
                        />
                      </div>
                    </div>
                  </div>

                  {/* Product Specifications */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground">Product Specifications</h3>

                    {/* Size Selection */}
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Size *
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {(['small', 'medium', 'large', 'extra-large'] as const).map(size => (
                          <Button
                            key={size}
                            type="button"
                            variant={specs.size === size ? 'default' : 'outline'}
                            onClick={() => handleSpecChange('size', size)}
                            className="capitalize"
                          >
                            {size.replace('-', ' ')}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Bedazzling Level */}
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Bedazzling Level *
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {(['light', 'medium', 'heavy', 'premium'] as const).map(level => (
                          <Button
                            key={level}
                            type="button"
                            variant={specs.bedazzlingLevel === level ? 'default' : 'outline'}
                            onClick={() => handleSpecChange('bedazzlingLevel', level)}
                            className="capitalize"
                          >
                            {level}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Frame Option */}
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Frame Option *
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        {(['basic', 'premium', 'luxury'] as const).map(frame => (
                          <Button
                            key={frame}
                            type="button"
                            variant={specs.frameOption === frame ? 'default' : 'outline'}
                            onClick={() => handleSpecChange('frameOption', frame)}
                            className="capitalize"
                          >
                            {frame}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Complexity */}
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Design Complexity *
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        {(['simple', 'detailed', 'intricate'] as const).map(complexity => (
                          <Button
                            key={complexity}
                            type="button"
                            variant={specs.complexity === complexity ? 'default' : 'outline'}
                            onClick={() => handleSpecChange('complexity', complexity)}
                            className="capitalize"
                          >
                            {complexity}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Quantity */}
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Quantity
                      </label>
                      <div className="flex items-center gap-3">
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => adjustQuantity(-1)}
                          disabled={specs.quantity <= 1}
                        >
                          <MinusIcon className="h-4 w-4" />
                        </Button>
                        <span className="text-lg font-semibold w-12 text-center">
                          {specs.quantity}
                        </span>
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => adjustQuantity(1)}
                        >
                          <PlusIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Rush Order */}
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="rushOrder"
                        checked={specs.rushOrder}
                        onChange={(e) => handleSpecChange('rushOrder', e.target.checked)}
                        className="rounded border-border"
                      />
                      <label htmlFor="rushOrder" className="text-sm font-medium text-foreground">
                        Rush Order (+50% fee, faster delivery)
                      </label>
                    </div>
                  </div>

                  {/* Additional Features */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground">Additional Features</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {ADDITIONAL_FEATURES.map(feature => (
                        <div key={feature.id} className="flex items-start space-x-3">
                          <input
                            type="checkbox"
                            id={feature.id}
                            checked={specs.additionalFeatures.includes(feature.id)}
                            onChange={() => toggleFeature(feature.id)}
                            className="mt-1 rounded border-border"
                          />
                          <div className="flex-1">
                            <label htmlFor={feature.id} className="text-sm font-medium text-foreground cursor-pointer">
                              {feature.name} (+{formatPrice(feature.price)})
                            </label>
                            <p className="text-xs text-muted-foreground">{feature.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Project Description */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Project Description *
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Describe your vision, the subject of the portrait, any specific requirements..."
                      className="w-full min-h-[120px] px-3 py-2 border border-border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-accent"
                      required
                    />
                  </div>

                  {/* Image Upload */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Reference Images
                    </label>
                    <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                      <PhotoIcon className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <p className="text-sm text-muted-foreground mb-2">
                        Upload photos you'd like us to transform
                      </p>
                      <input
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        id="image-upload"
                      />
                      <label htmlFor="image-upload">
                        <Button type="button" variant="outline" className="cursor-pointer">
                          Choose Files
                        </Button>
                      </label>
                      {formData.referenceImages.length > 0 && (
                        <p className="text-sm text-muted-foreground mt-2">
                          {formData.referenceImages.length} file(s) selected
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    size="lg"
                    className="w-full"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      'Submitting...'
                    ) : (
                      <>
                        Submit Custom Order Request
                        <ArrowRightIcon className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Price Estimation Sidebar */}
        <div className="lg:col-span-1">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="sticky top-8"
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CalculatorIcon className="h-5 w-5 text-accent" />
                  Price Estimation
                </CardTitle>
                <CardDescription>
                  Real-time pricing based on your selections
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Price Breakdown */}
                <div className="space-y-2">
                  {estimation.breakdown.map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="text-muted-foreground">{item.label}</span>
                      <span className={item.amount < 0 ? 'text-green-600' : 'text-foreground'}>
                        {item.amount < 0 ? '-' : ''}{formatPrice(Math.abs(item.amount))}
                      </span>
                    </div>
                  ))}
                </div>

                <div className="border-t border-border pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold text-foreground">Total</span>
                    <span className="text-2xl font-bold text-accent">
                      {formatPrice(estimation.total)}
                    </span>
                  </div>
                </div>

                {/* Delivery Time */}
                <div className="bg-muted/50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <ClockIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium text-foreground">Estimated Delivery</span>
                  </div>
                  <p className="text-sm text-muted-foreground">{deliveryTime.description}</p>
                </div>

                {/* Features Summary */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-foreground">Selected Features:</h4>
                  <div className="space-y-1">
                    <Badge variant="secondary" className="text-xs">
                      {specs.size.replace('-', ' ')} size
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      {specs.bedazzlingLevel} bedazzling
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      {specs.frameOption} frame
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      {specs.complexity} complexity
                    </Badge>
                    {specs.quantity > 1 && (
                      <Badge variant="secondary" className="text-xs">
                        Qty: {specs.quantity}
                      </Badge>
                    )}
                    {specs.rushOrder && (
                      <Badge variant="destructive" className="text-xs">
                        Rush Order
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Info Note */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-start gap-2">
                    <InformationCircleIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-xs text-blue-800 font-medium">Price Estimate</p>
                      <p className="text-xs text-blue-700">
                        This is an estimated price. Final quote will be provided after reviewing your requirements.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
                  <Input
                    type="date"
                    value={formData.deadline}
                    onChange={(e) => handleInputChange('deadline', e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Optional - typical turnaround is 3-5 weeks
                  </p>
                </div>
              </div>

              {/* Image Upload */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Reference Images
                </label>
                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                  <PhotoIcon className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-sm text-muted-foreground mb-2">
                    Upload photos you'd like us to transform
                  </p>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                  />
                  <label htmlFor="image-upload">
                    <Button type="button" variant="outline" className="cursor-pointer">
                      Choose Files
                    </Button>
                  </label>
                  {formData.referenceImages.length > 0 && (
                    <p className="text-xs text-muted-foreground mt-2">
                      {formData.referenceImages.length} file(s) selected
                    </p>
                  )}
                </div>
              </div>

              {/* Submit Button */}
              <div className="text-center">
                <Button
                  type="submit"
                  size="lg"
                  disabled={isSubmitting}
                  className="w-full md:w-auto"
                >
                  {isSubmitting ? (
                    'Submitting Request...'
                  ) : (
                    <>
                      Submit Custom Order Request
                      <ArrowRightIcon className="h-4 w-4 ml-2" />
                    </>
                  )}
                </Button>
                <p className="text-xs text-muted-foreground mt-2">
                  We'll review your request and get back to you within 24 hours
                </p>
              </div>
            </form>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
