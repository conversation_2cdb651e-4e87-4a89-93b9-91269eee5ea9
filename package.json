{"name": "dazzled", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"pnpm dev:backend\" \"pnpm dev:frontend\" --names \"backend,frontend\" --prefix-colors \"blue,green\"", "dev:frontend": "next dev", "dev:backend": "cd backend && pnpm start:dev", "dev:full": "concurrently \"pnpm dev:backend\" \"pnpm dev:frontend\" --names \"backend,frontend\" --prefix-colors \"blue,green\"", "build": "next build", "build:backend": "cd backend && pnpm build", "build:full": "pnpm build:backend && pnpm build", "start": "next start", "start:full": "concurrently \"cd backend && pnpm start:prod\" \"pnpm start\" --names \"backend,frontend\" --prefix-colors \"blue,green\"", "lint": "next lint", "lint:backend": "cd backend && pnpm lint", "lint:full": "pnpm lint && pnpm lint:backend", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:backend": "cd backend && pnpm test", "test:full": "pnpm test && pnpm test:backend", "validate-env": "node scripts/validate-env.js", "setup": "pnpm install && cd backend && pnpm install && cd .. && pnpm validate-env", "clean": "rm -rf .next && rm -rf backend/dist && rm -rf node_modules/.cache"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.5", "lucide-react": "^0.525.0", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20.19.8", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.4.1", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "typescript": "^5"}}